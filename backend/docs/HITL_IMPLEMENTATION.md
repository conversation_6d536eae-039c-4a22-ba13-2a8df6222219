# Human in the Loop (HITL) Implementation untuk Hotel Booking

## Overview

Implementasi Human in the Loop (HITL) menggunakan LangGraph untuk proses pemesanan dan pembayaran hotel. HITL memungkinkan intervensi manusia pada titik-titik kritis dalam workflow otomatis, memberikan kontrol dan validasi sebelum eksekusi tindakan penting.

## Fitur HITL yang Diimplementasikan

### 1. Hotel Booking Confirmation
- **Titik Interrupt**: Sebelum eksekusi `book_hotel_room`
- **Tujuan**: Konfirmasi detail pemesanan sebelum membuat booking
- **Data yang ditampilkan**: 
  - Detail hotel (nama, lokasi)
  - Tanggal check-in dan check-out
  - Tipe kamar dan jumlah kamar
  - Jumlah tamu
  - Total harga
  - Data pemesan (nama, email, telepon)

### 2. Hotel Payment Confirmation
- **Titik Interrupt**: Sebelum eksekusi `process_hotel_payment`
- **Tujuan**: Konfirmasi metode pembayaran dan jumlah sebelum memproses
- **Data yang ditampilkan**:
  - ID booking
  - Detail hotel
  - Nama pemesan
  - Total pembayaran
  - Metode pembayaran

## Arsitektur HITL

### State Management
```python
class State(TypedDict):
    # ... existing fields ...
    pending_hotel_booking: Optional[Dict[str, Any]]  # Data booking menunggu konfirmasi
    pending_hotel_payment: Optional[Dict[str, Any]]  # Data payment menunggu konfirmasi
    hitl_context: Optional[Dict[str, Any]]           # Context tambahan untuk HITL
```

### HITL Nodes
1. **hotel_booking_confirmation**: Node untuk konfirmasi booking
2. **hotel_payment_confirmation**: Node untuk konfirmasi payment
3. **process_hitl_instructions**: Node untuk memproses instruksi HITL

### HITL Tools
1. **book_hotel_room_with_hitl**: Tool booking dengan konfirmasi
2. **process_hotel_payment_with_hitl**: Tool payment dengan konfirmasi
3. **execute_approved_hotel_booking**: Tool eksekusi booking yang sudah diapprove
4. **execute_approved_hotel_payment**: Tool eksekusi payment yang sudah diapprove

## Flow HITL

### Hotel Booking Flow
```
User Request → Hotel Agent → book_hotel_room_with_hitl → 
process_hitl_instructions → hotel_booking_confirmation → 
[INTERRUPT] → User Decision → execute_approved_hotel_booking
```

### Hotel Payment Flow
```
User Request → Hotel Agent → process_hotel_payment_with_hitl → 
process_hitl_instructions → hotel_payment_confirmation → 
[INTERRUPT] → User Decision → execute_approved_hotel_payment
```

## Penggunaan

### 1. Booking Hotel dengan HITL
```python
# User message
"Saya ingin memesan hotel dengan book_hotel_room_with_hitl untuk hotel ID 1, 
check-in besok, check-out lusa, 2 tamu, 1 kamar standard"

# System akan menampilkan konfirmasi dan menunggu approval
# User dapat memilih: approve, reject, atau modify
```

### 2. Payment Hotel dengan HITL
```python
# User message  
"Bayar booking ID 123 dengan process_hotel_payment_with_hitl menggunakan kartu kredit"

# System akan menampilkan konfirmasi pembayaran dan menunggu approval
# User dapat memilih: approve, reject, atau change_method
```

## User Decision Options

### Booking Confirmation
- **approve**: Lanjutkan pemesanan
- **reject**: Batalkan pemesanan
- **modify**: Kembali ke agent untuk modifikasi

### Payment Confirmation
- **approve**: Lanjutkan pembayaran
- **reject**: Batalkan pembayaran
- **change_method**: Ubah metode pembayaran

## Interrupt Data Structure

### Booking Interrupt
```json
{
  "type": "hotel_booking_confirmation",
  "message": "Konfirmasi Pemesanan Hotel",
  "data": {
    "hotel_name": "Hotel ABC",
    "location": "Jakarta",
    "check_in_date": "2024-01-15",
    "check_out_date": "2024-01-16",
    "room_type": "Standard",
    "guest_count": 2,
    "room_count": 1,
    "total_price": 500000,
    "guest_name": "John Doe",
    "email": "<EMAIL>",
    "phone": "081234567890"
  },
  "question": "Apakah Anda yakin ingin melanjutkan pemesanan hotel ini?",
  "options": ["approve", "reject", "modify"]
}
```

### Payment Interrupt
```json
{
  "type": "hotel_payment_confirmation",
  "message": "Konfirmasi Pembayaran Hotel", 
  "data": {
    "booking_id": 123,
    "hotel_name": "Hotel ABC",
    "guest_name": "John Doe",
    "total_amount": 500000,
    "payment_method": "kartu kredit"
  },
  "question": "Apakah Anda yakin ingin melakukan pembayaran sebesar Rp 500,000?",
  "options": ["approve", "reject", "change_method"]
}
```

## Testing

Jalankan test script untuk menguji implementasi HITL:

```bash
cd backend
python test_hitl_hotel.py
```

## Keamanan dan Validasi

1. **Input Validation**: Semua input divalidasi sebelum interrupt
2. **State Persistence**: State disimpan dengan checkpointer untuk resume
3. **Error Handling**: Comprehensive error handling untuk semua edge cases
4. **Data Sanitization**: Data dibersihkan sebelum ditampilkan ke user

## Extensibility

Implementasi ini dapat diperluas untuk:
1. Flight booking HITL
2. Tour booking HITL  
3. Multi-step approval workflows
4. Role-based approval (admin, manager, user)
5. Audit trail untuk semua decisions

## Troubleshooting

### Common Issues
1. **Interrupt tidak muncul**: Pastikan checkpointer dikonfigurasi
2. **State tidak persist**: Periksa thread_id consistency
3. **Tools tidak ditemukan**: Pastikan tools sudah diimport dan ditambahkan ke tools list

### Debug Mode
Enable logging untuk debug:
```python
import logging
logging.basicConfig(level=logging.DEBUG)
```
