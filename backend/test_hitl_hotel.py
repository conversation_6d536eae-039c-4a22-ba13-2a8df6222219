#!/usr/bin/env python3
"""
Test script untuk Human in the Loop (HITL) implementation pada hotel booking.
Script ini mendemonstrasikan bagaimana HITL bekerja dalam proses pemesanan dan pembayaran hotel.
"""

import asyncio
import os
import sys
from datetime import datetime, timedelta

# Add backend to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from agents.graph import create_graph
from langgraph.checkpoint.memory import MemorySaver
from langgraph.types import Command

async def test_hitl_hotel_booking():
    """
    Test HITL untuk proses booking hotel.
    """
    print("🧪 Testing HITL Hotel Booking Implementation")
    print("=" * 50)
    
    # Create graph dengan checkpointer untuk HITL
    checkpointer = MemorySaver()
    graph = create_graph(checkpointer)
    
    # Thread config untuk persistence
    thread_config = {"configurable": {"thread_id": "test_hitl_hotel_001"}}
    
    # Test 1: Hotel booking dengan HITL
    print("\n📝 Test 1: Hotel Booking dengan HITL")
    print("-" * 30)
    
    # Tanggal untuk test (besok dan lusa)
    tomorrow = (datetime.now() + timedelta(days=1)).strftime("%Y-%m-%d")
    day_after = (datetime.now() + timedelta(days=2)).strftime("%Y-%m-%d")
    
    # Initial message untuk booking hotel
    initial_message = f"""Saya ingin memesan hotel dengan detail berikut:
- Hotel ID: 1
- Check-in: {tomorrow}
- Check-out: {day_after}
- Jumlah tamu: 2
- Jumlah kamar: 1
- Tipe kamar: Standard
- Nama pemesan: John Doe
- Email: <EMAIL>
- Telepon: 081234567890
- Catatan: Honeymoon trip

Gunakan book_hotel_room_with_hitl untuk pemesanan dengan konfirmasi."""
    
    try:
        # Run graph sampai interrupt
        print("🚀 Menjalankan booking request...")
        result = graph.invoke({"messages": [{"role": "user", "content": initial_message}]}, config=thread_config)
        
        # Check untuk interrupt
        if "__interrupt__" in result:
            interrupt_data = result["__interrupt__"]
            print(f"⏸️  HITL Interrupt detected:")
            print(f"   Type: {interrupt_data[0].value.get('type', 'unknown')}")
            print(f"   Message: {interrupt_data[0].value.get('message', 'No message')}")
            print(f"   Question: {interrupt_data[0].value.get('question', 'No question')}")
            
            # Display booking data
            booking_data = interrupt_data[0].value.get('data', {})
            if booking_data:
                print(f"\n📋 Booking Details:")
                print(f"   Hotel: {booking_data.get('hotel_name')} - {booking_data.get('location')}")
                print(f"   Dates: {booking_data.get('check_in_date')} to {booking_data.get('check_out_date')}")
                print(f"   Rooms: {booking_data.get('room_count')} x {booking_data.get('room_type')}")
                print(f"   Guests: {booking_data.get('guest_count')}")
                print(f"   Total: Rp {booking_data.get('total_price', 0):,}")
            
            # Simulate user approval
            print(f"\n✅ Simulating user approval...")
            approval_result = graph.invoke(Command(resume="approve"), config=thread_config)
            print(f"📄 Approval result: {approval_result}")
            
        else:
            print(f"❌ No interrupt detected. Result: {result}")
            
    except Exception as e:
        print(f"❌ Error in booking test: {str(e)}")
        import traceback
        traceback.print_exc()


async def test_hitl_hotel_payment():
    """
    Test HITL untuk proses payment hotel.
    """
    print("\n💳 Test 2: Hotel Payment dengan HITL")
    print("-" * 30)
    
    # Create new graph instance
    checkpointer = MemorySaver()
    graph = create_graph(checkpointer)
    
    # Thread config untuk persistence
    thread_config = {"configurable": {"thread_id": "test_hitl_payment_001"}}
    
    # Payment message
    payment_message = """Saya ingin melakukan pembayaran untuk pemesanan hotel:
- Booking ID: 1
- Metode pembayaran: kartu kredit

Gunakan process_hotel_payment_with_hitl untuk pembayaran dengan konfirmasi."""
    
    try:
        # Run graph sampai interrupt
        print("🚀 Menjalankan payment request...")
        result = graph.invoke({"messages": [{"role": "user", "content": payment_message}]}, config=thread_config)
        
        # Check untuk interrupt
        if "__interrupt__" in result:
            interrupt_data = result["__interrupt__"]
            print(f"⏸️  HITL Interrupt detected:")
            print(f"   Type: {interrupt_data[0].value.get('type', 'unknown')}")
            print(f"   Message: {interrupt_data[0].value.get('message', 'No message')}")
            print(f"   Question: {interrupt_data[0].value.get('question', 'No question')}")
            
            # Display payment data
            payment_data = interrupt_data[0].value.get('data', {})
            if payment_data:
                print(f"\n💰 Payment Details:")
                print(f"   Booking ID: {payment_data.get('booking_id')}")
                print(f"   Hotel: {payment_data.get('hotel_name')}")
                print(f"   Guest: {payment_data.get('guest_name')}")
                print(f"   Amount: Rp {payment_data.get('total_amount', 0):,}")
                print(f"   Method: {payment_data.get('payment_method')}")
            
            # Simulate user approval
            print(f"\n✅ Simulating user approval...")
            approval_result = graph.invoke(Command(resume="approve"), config=thread_config)
            print(f"📄 Approval result: {approval_result}")
            
        else:
            print(f"❌ No interrupt detected. Result: {result}")
            
    except Exception as e:
        print(f"❌ Error in payment test: {str(e)}")
        import traceback
        traceback.print_exc()


async def main():
    """
    Main test function.
    """
    print("🎯 HITL Hotel Implementation Test Suite")
    print("=" * 60)
    
    # Test booking HITL
    await test_hitl_hotel_booking()
    
    # Test payment HITL
    await test_hitl_hotel_payment()
    
    print("\n🏁 Test suite completed!")
    print("=" * 60)


if __name__ == "__main__":
    asyncio.run(main())
